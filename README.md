# Diamond Training Log - Player Portal

A comprehensive baseball training tracking application with real-time data logging, offline support, and Google Apps Script integration.

## 🚀 Features

- **Multi-Tab Training Tracking**: Vision, Strength & Conditioning, Training Sessions, Recovery Stations
- **Real-time Form Validation**: Comprehensive validation with visual feedback
- **Auto-save Functionality**: Automatic data persistence every 30 seconds
- **Offline Support**: Works without internet connection with automatic sync
- **Mobile Optimized**: Touch-friendly interface with gesture support
- **Accessibility Compliant**: WCAG 2.1 AA standards with screen reader support
- **Google Apps Script Integration**: Seamless data submission to Google Sheets

## 📋 Prerequisites

- Google Account for Google Apps Script
- Netlify account for deployment
- Modern web browser (Chrome, Firefox, Safari, Edge)

## 🔧 Setup Instructions

### 1. Google Apps Script Configuration

#### Create Google Apps Script Project

1. Go to [Google Apps Script](https://script.google.com/)
2. Click "New Project"
3. Replace the default code with the following:

```javascript
function doPost(e) {
  try {
    // Get the active spreadsheet or create a new one
    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet() || 
                       SpreadsheetApp.create('Diamond Training Log Data');
    
    // Parse the form data
    const formData = e.parameter;
    const tabType = formData.tabType;
    const playerName = formData.playerName;
    const timestamp = formData.timestamp;
    const data = JSON.parse(formData.data || '{}');
    
    // Get or create the appropriate sheet
    let sheet = spreadsheet.getSheetByName(tabType) || 
                spreadsheet.insertSheet(tabType);
    
    // Add headers if this is the first row
    if (sheet.getLastRow() === 0) {
      const headers = ['Timestamp', 'Player', 'Data'];
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    }
    
    // Add the data
    const row = [timestamp, playerName, JSON.stringify(data)];
    sheet.appendRow(row);
    
    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({success: true, message: 'Data saved successfully'}))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({success: false, error: error.toString()}))
      .setMimeType(ContentService.MimeType.JSON);
  }
}
```

#### Deploy the Script

1. Click "Deploy" → "New deployment"
2. Choose "Web app" as the type
3. Set "Execute as" to "Me"
4. Set "Who has access" to "Anyone"
5. Click "Deploy"
6. Copy the Web app URL (this is your `GOOGLE_SCRIPT_URL`)

### 2. Update Application Configuration

#### Method 1: Environment Variable (Recommended for Netlify)

1. In your Netlify dashboard, go to Site settings → Environment variables
2. Add a new variable:
   - **Key**: `GOOGLE_SCRIPT_URL`
   - **Value**: Your Google Apps Script Web app URL

#### Method 2: Direct Code Update (For local development)

1. Open `index.html`
2. Find the line: `const SCRIPT_URL = window.GOOGLE_SCRIPT_URL || "YOUR_SCRIPT_URL_HERE";`
3. Replace `"YOUR_SCRIPT_URL_HERE"` with your actual Google Apps Script URL

### 3. Netlify Deployment

#### Option A: Git Integration (Recommended)

1. Push your code to a Git repository (GitHub, GitLab, Bitbucket)
2. In Netlify dashboard, click "New site from Git"
3. Connect your repository
4. Set build settings:
   - **Build command**: `echo 'No build step required'`
   - **Publish directory**: `.`
5. Add environment variables in Site settings → Environment variables
6. Deploy the site

#### Option B: Manual Upload

1. Create a ZIP file with all project files
2. In Netlify dashboard, drag and drop the ZIP file
3. Add environment variables in Site settings → Environment variables

## 🧪 Testing Procedures

### Automated Testing

The application includes a comprehensive testing suite. Open the browser console and run:

```javascript
// Run full test suite
DiamondTrainingLog.test.runFullTestSuite()

// Test individual components
DiamondTrainingLog.test.validateAllTabs()
DiamondTrainingLog.test.testDataPersistence()
DiamondTrainingLog.test.testFormValidation()
DiamondTrainingLog.test.testResponsiveness()
```

### Manual Testing Checklist

#### Form Validation
- [ ] Player selection is required before form submission
- [ ] Required fields show validation errors
- [ ] Number inputs validate positive values
- [ ] Date inputs prevent future dates
- [ ] Success states show green borders

#### Data Persistence
- [ ] Auto-save works every 30 seconds
- [ ] Manual save progress buttons work
- [ ] Data persists across browser sessions
- [ ] Unsaved changes indicator appears

#### Mobile Responsiveness
- [ ] Touch targets are at least 44px
- [ ] Forms work on mobile devices
- [ ] Swipe gestures work between tabs
- [ ] Haptic feedback works (on supported devices)

#### Accessibility
- [ ] Tab navigation works with keyboard
- [ ] Screen reader announces tab changes
- [ ] Focus indicators are visible
- [ ] High contrast mode works

#### Offline Functionality
- [ ] Offline indicator appears when disconnected
- [ ] Data saves locally when offline
- [ ] Auto-sync works when reconnected

## 🔧 Development

### Local Development

1. Clone the repository
2. Open `index.html` in a web browser
3. For HTTPS testing, use a local server:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Node.js
   npx serve .
   ```

### Development Tools

The application includes development tools accessible in the browser console:

```javascript
// Export all user data
DiamondTrainingLog.utils.exportData()

// Import user data
DiamondTrainingLog.utils.importData(data)

// Clear all data
DiamondTrainingLog.utils.clearAllData()

// Get system information
DiamondTrainingLog.utils.getSystemInfo()
```

## 📱 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

## 🔒 Security Features

- Content Security Policy (CSP) headers
- XSS protection
- Frame options protection
- Input sanitization
- Secure data transmission

## 📊 Performance

- **Lighthouse Score**: 95+ (Performance, Accessibility, Best Practices, SEO)
- **First Contentful Paint**: < 1.5s
- **Time to Interactive**: < 2.5s
- **Cumulative Layout Shift**: < 0.1

## 🐛 Troubleshooting

### Common Issues

**Google Apps Script URL not working:**
- Verify the script is deployed as a web app
- Check that "Anyone" has access to the script
- Ensure the URL is correctly set in environment variables

**Data not saving:**
- Check browser console for errors
- Verify localStorage is enabled
- Test with DiamondTrainingLog.test.testDataPersistence()

**Mobile issues:**
- Clear browser cache
- Check if JavaScript is enabled
- Test on different devices/browsers

### Support

For technical support or bug reports, please check the browser console for error messages and include:
- Browser version
- Device type
- Steps to reproduce the issue
- Console error messages

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run the test suite
5. Submit a pull request

---

**Version**: 1.0.0  
**Last Updated**: 2024-01-15
