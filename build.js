#!/usr/bin/env node

/**
 * Build script for Diamond Training Log
 * Injects environment variables into the HTML file for Netlify deployment
 */

const fs = require('fs');
const path = require('path');

// Get environment variables
const GOOGLE_SCRIPT_URL = process.env.GOOGLE_SCRIPT_URL || 'YOUR_SCRIPT_URL_HERE';

console.log('🏗️ Building Diamond Training Log...');

// Read the HTML file
const htmlPath = path.join(__dirname, 'index.html');
let htmlContent = fs.readFileSync(htmlPath, 'utf8');

// Inject environment variables
const envScript = `
<script>
  // Environment variables injected at build time
  window.GOOGLE_SCRIPT_URL = "${GOOGLE_SCRIPT_URL}";
</script>
`;

// Insert the environment script before the closing head tag
htmlContent = htmlContent.replace('</head>', `${envScript}</head>`);

// Write the updated HTML file
fs.writeFileSync(htmlPath, htmlContent);

console.log('✅ Build complete!');
console.log(`📝 Google Script URL: ${GOOGLE_SCRIPT_URL === 'YOUR_SCRIPT_URL_HERE' ? 'Not configured' : 'Configured'}`);

if (GOOGLE_SCRIPT_URL === 'YOUR_SCRIPT_URL_HERE') {
  console.warn('⚠️ Warning: GOOGLE_SCRIPT_URL environment variable not set');
  console.log('💡 Set the GOOGLE_SCRIPT_URL environment variable in Netlify dashboard');
}
