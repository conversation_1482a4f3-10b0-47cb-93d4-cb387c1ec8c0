{"name": "diamond-training-log", "version": "1.0.0", "description": "A comprehensive baseball training tracking application", "main": "index.html", "scripts": {"build": "node build.js", "dev": "http-server -p 3000 -o", "test": "echo \"Run DiamondTrainingLog.test.runFullTestSuite() in browser console\"", "lint": "echo \"No linting configured - pure HTML/CSS/JS project\"", "deploy": "echo \"Deploy via Netlify dashboard or Git integration\""}, "keywords": ["baseball", "training", "sports", "tracking", "web-app", "pwa"], "author": "Diamond Training Log Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/diamond-training-log.git"}, "bugs": {"url": "https://github.com/your-username/diamond-training-log/issues"}, "homepage": "https://your-site.netlify.app", "engines": {"node": ">=18.0.0"}, "devDependencies": {"http-server": "^14.1.1"}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}