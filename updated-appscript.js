// Configuration
const CONFIG = {
  SPREADSHEET_ID: "1lgKCQKJFfksWaddED2cN--nOnJHntTbRhEBQ_27N4WU",
  DATA_SHEET_NAME: "TrainingData",
};

// Expanded headers for better data visualization
const HEADERS = [
  "Player Name", "Timestamp", "Date", "Session ID", "Form Type",
  "Lift Name", "Sets", "Reps", "Weight", "Is Max",
  "Training Type", "Working On", "Success Rating",
  "Recovery Method", "Duration (Minutes)",
  "Pitching Focus", "Pitching Rating", "Hitting Focus", "Hitting Rating",
  "Notes", "Raw Data"
];

// Entry point for iframe form submissions
function doPost(e) {
  try {
    const data = e.parameter || {};
    console.log('Received data:', JSON.stringify(data));
    
    const formType = data.formType;
    if (!formType) {
      throw new Error("Missing 'formType' field in form submission.");
    }

    const spreadsheet = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    let sheet = spreadsheet.getSheetByName(CONFIG.DATA_SHEET_NAME);
    if (!sheet) {
      sheet = spreadsheet.insertSheet(CONFIG.DATA_SHEET_NAME);
    }

    // Ensure headers exist
    if (sheet.getLastRow() === 0) {
      sheet.getRange(1, 1, 1, HEADERS.length).setValues([HEADERS]);
    }

    // Handle different form types
    let rows = [];
    
    if (formType === 'strength') {
      rows = processStrengthData(data);
    } else if (formType === 'training') {
      rows = processTrainingData(data);
    } else if (formType === 'recovery') {
      rows = processRecoveryData(data);
    } else if (formType === 'allData') {
      rows = processAllData(data);
    } else {
      // Fallback: save raw data
      rows = processRawData(data);
    }

    if (rows.length > 0) {
      const startRow = sheet.getLastRow() + 1;
      sheet.getRange(startRow, 1, rows.length, HEADERS.length).setValues(rows);
    }

    return ContentService.createTextOutput(JSON.stringify({ success: true }))
                         .setMimeType(ContentService.MimeType.JSON);
  } catch (error) {
    console.error("Error in doPost:", error);
    return ContentService.createTextOutput(JSON.stringify({ success: false, error: error.message }))
                         .setMimeType(ContentService.MimeType.JSON);
  }
}

// Process strength form data from Diamond Training Tool
function processStrengthData(data) {
  const rows = [];
  const baseInfo = {
    player: data.player || '',
    timestamp: data.timestamp || new Date().toISOString(),
    date: data.date || new Date().toISOString().split("T")[0],
    sessionId: generateSessionId()
  };

  // Parse lifts data (comes as JSON string)
  let lifts = [];
  try {
    lifts = JSON.parse(data.lifts || '[]');
  } catch (e) {
    console.error('Error parsing lifts:', e);
  }

  lifts.forEach(lift => {
    const row = new Array(HEADERS.length).fill('');
    row[0] = baseInfo.player;
    row[1] = baseInfo.timestamp;
    row[2] = Array.isArray(baseInfo.date) ? baseInfo.date.join(', ') : baseInfo.date;
    row[3] = baseInfo.sessionId;
    row[4] = 'Strength & Conditioning';
    row[5] = lift.name || '';
    row[6] = lift.sets || '';
    row[7] = lift.reps || '';
    row[8] = lift.weight || '';
    row[9] = lift.isMax ? 'Yes' : 'No';
    row[10] = 'Strength Training';
    row[20] = JSON.stringify(lift);
    rows.push(row);
  });

  return rows;
}

// Process training data (hitting/pitching)
function processTrainingData(data) {
  const rows = [];
  const baseInfo = {
    player: data.player || '',
    timestamp: data.timestamp || new Date().toISOString(),
    sessionId: generateSessionId()
  };

  // Parse hitting data
  if (data.hitting) {
    let hitting = {};
    try {
      hitting = JSON.parse(data.hitting);
    } catch (e) {
      console.error('Error parsing hitting data:', e);
    }

    if (hitting && Object.keys(hitting).length > 0) {
      const row = new Array(HEADERS.length).fill('');
      row[0] = baseInfo.player;
      row[1] = baseInfo.timestamp;
      row[2] = Array.isArray(hitting.dates) ? hitting.dates.join(', ') : (hitting.date || new Date().toISOString().split("T")[0]);
      row[3] = baseInfo.sessionId;
      row[4] = 'Training';
      row[10] = 'Hitting';
      row[11] = hitting.focus || '';
      row[12] = hitting.rating || '';
      row[17] = hitting.focus || '';
      row[18] = hitting.rating || '';
      row[20] = JSON.stringify(hitting);
      rows.push(row);
    }
  }

  // Parse pitching data
  if (data.pitching) {
    let pitching = {};
    try {
      pitching = JSON.parse(data.pitching);
    } catch (e) {
      console.error('Error parsing pitching data:', e);
    }

    if (pitching && Object.keys(pitching).length > 0) {
      const row = new Array(HEADERS.length).fill('');
      row[0] = baseInfo.player;
      row[1] = baseInfo.timestamp;
      row[2] = Array.isArray(pitching.dates) ? pitching.dates.join(', ') : (pitching.date || new Date().toISOString().split("T")[0]);
      row[3] = baseInfo.sessionId;
      row[4] = 'Training';
      row[10] = 'Pitching';
      row[11] = pitching.focus || '';
      row[12] = pitching.rating || '';
      row[15] = pitching.focus || '';
      row[16] = pitching.rating || '';
      row[20] = JSON.stringify(pitching);
      rows.push(row);
    }
  }

  return rows;
}

// Process recovery data
function processRecoveryData(data) {
  const rows = [];
  const baseInfo = {
    player: data.player || '',
    timestamp: data.timestamp || new Date().toISOString(),
    date: data.date || new Date().toISOString().split("T")[0],
    sessionId: generateSessionId()
  };

  // Parse recovery items
  let items = [];
  try {
    items = JSON.parse(data.items || '[]');
  } catch (e) {
    console.error('Error parsing recovery items:', e);
  }

  items.forEach(item => {
    const row = new Array(HEADERS.length).fill('');
    row[0] = baseInfo.player;
    row[1] = baseInfo.timestamp;
    row[2] = Array.isArray(baseInfo.date) ? baseInfo.date.join(', ') : baseInfo.date;
    row[3] = baseInfo.sessionId;
    row[4] = 'Recovery';
    row[10] = 'Recovery Session';
    row[13] = item.method || '';
    row[14] = item.minutes || '';
    row[20] = JSON.stringify(item);
    rows.push(row);
  });

  return rows;
}

// Process all data submission - break down into individual records
function processAllData(data) {
  const rows = [];
  const baseInfo = {
    player: data.player || '',
    timestamp: data.timestamp || new Date().toISOString(),
    sessionId: generateSessionId()
  };

  try {
    // Process workouts from the all data submission
    const workouts = JSON.parse(data.workouts || '[]');
    workouts.forEach(workout => {
      if (workout.lifts && Array.isArray(workout.lifts)) {
        workout.lifts.forEach(lift => {
          const row = new Array(HEADERS.length).fill('');
          row[0] = baseInfo.player;
          row[1] = baseInfo.timestamp;
          row[2] = workout.date || new Date().toISOString().split("T")[0];
          row[3] = baseInfo.sessionId;
          row[4] = 'Strength & Conditioning';
          row[5] = lift.name || '';
          row[6] = lift.sets || '';
          row[7] = lift.reps || '';
          row[8] = lift.weight || '';
          row[9] = lift.isMax ? 'Yes' : 'No';
          row[10] = 'Strength Training';
          row[19] = 'From All Data Submission';
          row[20] = JSON.stringify(lift);
          rows.push(row);
        });
      }
    });

    // Process training sessions
    const trainingSessions = JSON.parse(data.trainingSessions || '[]');
    trainingSessions.forEach(session => {
      if (session.hitting) {
        const row = new Array(HEADERS.length).fill('');
        row[0] = baseInfo.player;
        row[1] = baseInfo.timestamp;
        row[2] = session.hitting.date || new Date().toISOString().split("T")[0];
        row[3] = baseInfo.sessionId;
        row[4] = 'Training';
        row[10] = 'Hitting';
        row[11] = session.hitting.focus || '';
        row[12] = session.hitting.rating || '';
        row[17] = session.hitting.focus || '';
        row[18] = session.hitting.rating || '';
        row[19] = 'From All Data Submission';
        row[20] = JSON.stringify(session.hitting);
        rows.push(row);
      }

      if (session.pitching) {
        const row = new Array(HEADERS.length).fill('');
        row[0] = baseInfo.player;
        row[1] = baseInfo.timestamp;
        row[2] = session.pitching.date || new Date().toISOString().split("T")[0];
        row[3] = baseInfo.sessionId;
        row[4] = 'Training';
        row[10] = 'Pitching';
        row[11] = session.pitching.focus || '';
        row[12] = session.pitching.rating || '';
        row[15] = session.pitching.focus || '';
        row[16] = session.pitching.rating || '';
        row[19] = 'From All Data Submission';
        row[20] = JSON.stringify(session.pitching);
        rows.push(row);
      }
    });

    // Process recovery sessions
    const recoverySessions = JSON.parse(data.recoverySessions || '[]');
    recoverySessions.forEach(recovery => {
      if (recovery.items && Array.isArray(recovery.items)) {
        recovery.items.forEach(item => {
          const row = new Array(HEADERS.length).fill('');
          row[0] = baseInfo.player;
          row[1] = baseInfo.timestamp;
          row[2] = recovery.date || new Date().toISOString().split("T")[0];
          row[3] = baseInfo.sessionId;
          row[4] = 'Recovery';
          row[10] = 'Recovery Session';
          row[13] = item.method || '';
          row[14] = item.minutes || '';
          row[19] = 'From All Data Submission';
          row[20] = JSON.stringify(item);
          rows.push(row);
        });
      }
    });

  } catch (error) {
    console.error('Error processing all data:', error);
    // Fallback: add a summary row if parsing fails
    const summaryRow = new Array(HEADERS.length).fill('');
    summaryRow[0] = baseInfo.player;
    summaryRow[1] = baseInfo.timestamp;
    summaryRow[2] = new Date().toISOString().split("T")[0];
    summaryRow[3] = baseInfo.sessionId;
    summaryRow[4] = 'All Data Submission (Raw)';
    summaryRow[19] = 'Error parsing - raw data preserved';
    summaryRow[20] = JSON.stringify(data);
    rows.push(summaryRow);
  }

  return rows;
}

// Fallback for unknown data types
function processRawData(data) {
  const row = new Array(HEADERS.length).fill('');
  row[0] = data.player || '';
  row[1] = data.timestamp || new Date().toISOString();
  row[2] = new Date().toISOString().split("T")[0];
  row[3] = generateSessionId();
  row[4] = data.formType || 'Unknown';
  row[19] = 'Unknown form type - raw data';
  row[20] = JSON.stringify(data);
  return [row];
}

// Generate unique session ID
function generateSessionId() {
  return 'session_' + new Date().getTime() + '_' + Math.random().toString(36).substring(2, 11);
}
