# Deployment Checklist

## Pre-Deployment

### 1. Google Apps Script Setup
- [ ] Created Google Apps Script project
- [ ] Added the provided doPost function
- [ ] Deployed as web app with "Anyone" access
- [ ] Copied the web app URL
- [ ] Tested the script with a sample POST request

### 2. Environment Configuration
- [ ] Set `GOOGLE_SCRIPT_URL` environment variable in Netlify
- [ ] Verified environment variable is not empty
- [ ] Tested locally with the script URL

### 3. Code Quality
- [ ] Ran `DiamondTrainingLog.test.runFullTestSuite()` in browser console
- [ ] All tests passing
- [ ] No console errors
- [ ] Validated HTML structure
- [ ] Checked accessibility with screen reader

## Netlify Deployment

### 4. Repository Setup
- [ ] Code pushed to Git repository (GitHub/GitLab/Bitbucket)
- [ ] Repository is public or <PERSON><PERSON> has access
- [ ] All files included (index.html, netlify.toml, _headers, etc.)

### 5. Netlify Configuration
- [ ] Connected repository to Netlify
- [ ] Build command set to: `node build.js`
- [ ] Publish directory set to: `.`
- [ ] Environment variables configured:
  - [ ] `GOOGLE_SCRIPT_URL` = Your Google Apps Script URL
  - [ ] `NODE_VERSION` = 18 (if needed)

### 6. Build Verification
- [ ] Build completed successfully
- [ ] No build errors in Netlify logs
- [ ] Environment variables injected correctly
- [ ] Site accessible at Netlify URL

## Post-Deployment Testing

### 7. Functionality Testing
- [ ] Player selection works
- [ ] All tabs load correctly
- [ ] Form validation working
- [ ] Data submission to Google Apps Script successful
- [ ] Auto-save functionality working
- [ ] Offline detection working

### 8. Performance Testing
- [ ] Page loads in under 3 seconds
- [ ] Lighthouse score > 90 for all metrics
- [ ] Mobile performance acceptable
- [ ] No JavaScript errors in console

### 9. Cross-Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

### 10. Accessibility Testing
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] High contrast mode support
- [ ] Focus indicators visible
- [ ] ARIA labels present

### 11. Mobile Testing
- [ ] Touch targets at least 44px
- [ ] Responsive design works on all screen sizes
- [ ] Swipe gestures work
- [ ] Forms usable on mobile
- [ ] No horizontal scrolling

### 12. Security Testing
- [ ] HTTPS enabled
- [ ] Security headers present
- [ ] Content Security Policy working
- [ ] No mixed content warnings
- [ ] XSS protection active

## Production Monitoring

### 13. Analytics Setup (Optional)
- [ ] Google Analytics configured
- [ ] Error tracking setup
- [ ] Performance monitoring active
- [ ] User feedback collection

### 14. Backup and Recovery
- [ ] Google Apps Script data backup plan
- [ ] Repository backup verified
- [ ] Environment variables documented
- [ ] Recovery procedures documented

## Troubleshooting

### Common Issues and Solutions

**Build Fails:**
- Check Node.js version (should be 18+)
- Verify build.js file exists
- Check Netlify build logs for specific errors

**Environment Variables Not Working:**
- Verify variable names match exactly
- Check for typos in variable values
- Redeploy after changing environment variables

**Google Apps Script Not Receiving Data:**
- Verify script is deployed as web app
- Check script permissions (should be "Anyone")
- Test script URL directly in browser
- Check CORS settings

**Forms Not Submitting:**
- Check browser console for JavaScript errors
- Verify Google Apps Script URL is correct
- Test with network tab open to see requests
- Check if offline mode is interfering

**Mobile Issues:**
- Clear browser cache
- Test on actual devices, not just browser dev tools
- Check for JavaScript errors on mobile
- Verify touch events are working

## Success Criteria

✅ **Deployment is successful when:**
- Site loads without errors
- All forms submit data successfully
- Google Apps Script receives and stores data
- Mobile experience is smooth
- Accessibility requirements met
- Performance scores are acceptable
- No security warnings

## Rollback Plan

If deployment fails:
1. Revert to previous Git commit
2. Redeploy from working commit
3. Check environment variables
4. Verify Google Apps Script is still working
5. Test core functionality

## Support Contacts

- **Technical Issues**: Check browser console and Netlify logs
- **Google Apps Script**: Verify script deployment and permissions
- **Netlify Support**: Use Netlify support channels for platform issues

---

**Last Updated**: 2024-01-15  
**Version**: 1.0.0
